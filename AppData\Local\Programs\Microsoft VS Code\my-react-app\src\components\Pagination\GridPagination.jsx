import React from 'react';

const GridPagination = ({ 
    totalRecords, 
    currentPage, 
    recordsPerPage, 
    onPageChange, 
    sortColumnName, 
    sortDirection 
}) => {
    const totalPages = Math.ceil(totalRecords / recordsPerPage);
    
    if (totalPages <= 1) {
        return null;
    }

    const handlePageClick = (pageNumber) => {
        if (pageNumber !== currentPage && pageNumber >= 1 && pageNumber <= totalPages) {
            onPageChange(sortColumnName, sortDirection, pageNumber);
        }
    };

    const renderPageNumbers = () => {
        const pages = [];
        const maxVisiblePages = 10;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        // Adjust start page if we're near the end
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // First page and ellipsis
        if (startPage > 1) {
            pages.push(
                <button
                    key={1}
                    className={`btn btn-sm ${currentPage === 1 ? 'btn-primary' : 'btn-outline-primary'} mx-1`}
                    onClick={() => handlePageClick(1)}
                >
                    1
                </button>
            );
            if (startPage > 2) {
                pages.push(<span key="start-ellipsis" className="mx-1">...</span>);
            }
        }

        // Page numbers
        for (let i = startPage; i <= endPage; i++) {
            pages.push(
                <button
                    key={i}
                    className={`btn btn-sm ${currentPage === i ? 'btn-primary' : 'btn-outline-primary'} mx-1`}
                    onClick={() => handlePageClick(i)}
                >
                    {i}
                </button>
            );
        }

        // Last page and ellipsis
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                pages.push(<span key="end-ellipsis" className="mx-1">...</span>);
            }
            pages.push(
                <button
                    key={totalPages}
                    className={`btn btn-sm ${currentPage === totalPages ? 'btn-primary' : 'btn-outline-primary'} mx-1`}
                    onClick={() => handlePageClick(totalPages)}
                >
                    {totalPages}
                </button>
            );
        }

        return pages;
    };

    return (
        <div className="d-flex align-items-center">
            <div className="me-3">
                <small className="text-muted">
                    Showing {((currentPage - 1) * recordsPerPage) + 1} to {Math.min(currentPage * recordsPerPage, totalRecords)} of {totalRecords} entries
                </small>
            </div>
            <div className="d-flex align-items-center">
                <button
                    className="btn btn-sm btn-outline-primary mx-1"
                    onClick={() => handlePageClick(currentPage - 1)}
                    disabled={currentPage === 1}
                >
                    Previous
                </button>
                
                {renderPageNumbers()}
                
                <button
                    className="btn btn-sm btn-outline-primary mx-1"
                    onClick={() => handlePageClick(currentPage + 1)}
                    disabled={currentPage === totalPages}
                >
                    Next
                </button>
            </div>
        </div>
    );
};

export default GridPagination;
