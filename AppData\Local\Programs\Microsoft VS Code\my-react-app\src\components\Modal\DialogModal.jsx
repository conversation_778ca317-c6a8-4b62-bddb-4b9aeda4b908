import React from 'react';
import { Mo<PERSON>, But<PERSON> } from 'react-bootstrap';

export const DialogModal = ({ ModalConfig }) => {
    const {
        showModal,
        title,
        infoMessage,
        showCloseButton = true,
        closetoggleModal
    } = ModalConfig;

    return (
        <Modal show={showModal} onHide={closetoggleModal} centered>
            <Modal.Header closeButton={showCloseButton}>
                <Modal.Title>{title}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <p>{infoMessage}</p>
            </Modal.Body>
            <Modal.Footer>
                <Button variant="secondary" onClick={closetoggleModal}>
                    Close
                </Button>
            </Modal.Footer>
        </Modal>
    );
};
