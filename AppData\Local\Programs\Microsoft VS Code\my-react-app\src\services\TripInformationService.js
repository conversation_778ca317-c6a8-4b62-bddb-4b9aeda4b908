export const TripInformationService = {
    getTripInformationReport: async (criteria) => {
        // Mock implementation - replace with actual API call
        await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay
        
        const mockData = [];
        const totalRecords = 125; // Mock total records
        
        // Generate mock data based on criteria
        const startIndex = criteria.SkipRecords || 0;
        const pageSize = criteria.PageSize || 50;
        
        for (let i = 0; i < Math.min(pageSize, totalRecords - startIndex); i++) {
            const index = startIndex + i + 1;
            mockData.push({
                id: index,
                prisonAssigned: criteria.Institution || `Prison ${index}`,
                routeType: `Route Type ${index}`,
                tripName: `Trip ${index}`,
                route: `Route ${index}`,
                returnTrip: index % 2 === 0 ? 'Yes' : 'No',
                licensePlate: `ABC-${index.toString().padStart(3, '0')}`,
                outOfService: index % 10 === 0 ? '1' : '0',
                capacity: 20 + (index % 10)
            });
        }
        
        return {
            reportData: mockData,
            totalRowCount: totalRecords
        };
    }
};
