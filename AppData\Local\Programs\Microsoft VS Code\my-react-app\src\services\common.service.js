export const CommonService = {
    getInstitutions: async () => {
        // Mock implementation - replace with actual API call
        return [
            { instCode: 'INST001', institutionId: 1, institutionName: 'Institution 1' },
            { instCode: 'INST002', institutionId: 2, institutionName: 'Institution 2' },
            { instCode: 'INST003', institutionId: 3, institutionName: 'Institution 3' }
        ];
    },

    downloadCSV: (csvContent, fileName) => {
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
};
