import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Card } from 'react-bootstrap';

import './TripList.css';
import { CommonService } from 'services/common.service';
import { TripInformationService } from 'services/TripInformationService';
import GridPagination from 'components/Pagination/GridPagination';
import LoadingComponent from 'components/Loading/LoadingComponent';
import messages from 'helpers/messages';
import { DialogModal } from 'components/Modal/DialogModal';
import modalConfig from 'components/Modal/ModalConfig';
import { faBold } from '@fortawesome/free-solid-svg-icons';

function TripList({ data }) {
    const navigate = useNavigate();
    const location = useLocation();

    const [searchParams] = useSearchParams();
    let instValue = searchParams.get("prisonAssigned");

    const { userProfile: userProfileState } = useSelector(x => x.userProfile);
    const userInstitutions = useRef([]);

    //const resetBtnRef = useRef(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isDataLoading, setIsDataLoading] = useState(false);
    const reportMessage = messages.StaffContactReport;

    //FormData
    const [formData, setFormData] = useState({
        selectedInstitution: '0',
        institutionList: [],
        totalRecords: 0,
        currentPage: 1,
        sortColumnName: 'PrisonAssigned',
        sortColumnDirection: 'ASC',
        reportData: [],
        //errorMessage: '',  
        isLoading: false
    });

    const recordsPerPageRef = useRef(50);

    //Errors
    const [showError, setShowError] = useState(false);
    const [formValidationMessage, setFormValidationMessage] = useState('');

    const [modalState, setModalState] = useState(modalConfig);
   
    useEffect(() => {
        loadFormData();
    }, []);

    
    useEffect(() => {

        if (instValue != null) {
            setFormData((prevState) => ({ ...prevState, selectedInstitution: instValue }));
        } else {
            handleResetPage();
        }
        

    }, [instValue]);

    // -- After parameter from  any screen trip list trigger Search
    useEffect(() => {

        if (instValue != null && instValue == formData.selectedInstitution) {
            handleSearch(null);
            instValue = null; //Reset the value to stop re-triggering the search
        }

    }, [formData.selectedInstitution]);


    const loadFormData = async () => {
        try {
            setIsLoading(true);

            if (!!userProfileState) {
                
                if (userProfileState.allowedfor !== undefined && userProfileState.allowedfor.length > 2) {
                    userInstitutions.current.push(userProfileState.allowedfor);
                }

                if (userProfileState.allowedfor2 !== undefined && userProfileState.allowedfor2.length > 2) {
                    userInstitutions.current.push(userProfileState.allowedfor2);
                }

                if (userProfileState.allowedfor3 !== undefined && userProfileState.allowedfor3.length > 2) {
                    userInstitutions.current.push(userProfileState.allowedfor3);
                }

                if (userProfileState.allowedfor4 !== undefined && userProfileState.allowedfor4.length > 2) {
                    userInstitutions.current.push(userProfileState.allowedfor4);
                }

                if (userProfileState.allowedfor5 !== undefined && userProfileState.allowedfor5.length > 2) {
                    userInstitutions.current.push(userProfileState.allowedfor5);
                }                
            }
            
            const institutionList = await CommonService.getInstitutions();
            setFormData((prevData) => ({ ...prevData, institutionList: institutionList }));

            setIsLoading(false);
        }
        catch (ex) {
            setIsLoading(false);

            setFormValidationMessage(reportMessage.FAILED_INSTITUTION_MSG);
            setShowError(true);
            console.log(ex);
        }
    };

    const clearGrid = (newSelection = '') => {
        setFormData(prev => ({
            ...prev,
            selectedInstitution: newSelection,
            reportData: [],
            totalRecords: 0,
            currentPage: 1,
            sortColumnName: 'PrisonAssigned',
            sortColumnDirection: 'ASC',
            institutionList: prev.institutionList,
            isLoading: false
        }));
        setShowError(false);
        setFormValidationMessage('');
    };


    //Handle events
    const handleInstitutionChange = (e) => {
        resetGrid();

        setFormData((prevData) => ({ ...prevData, selectedInstitution: e.target.value }));

    };

    //Search button click

    const handleSearch = async (e, institutionOverride) => {
        setShowError(false);
        setFormValidationMessage('');
        setIsDataLoading(true);

        // pick up the new value if passed, otherwise fall back to state
        const institution = institutionOverride != null
            ? institutionOverride
            : formData.selectedInstitution;

        const criteria = {
            PageSize: recordsPerPageRef.current,
            SkipRecords: (formData.currentPage - 1) * recordsPerPageRef.current,
            SortName: formData.sortColumnName || 'PrisonAssigned',
            SortDirection: formData.sortColumnDirection || 'ASC',
            Institution: institution
        };

        try {
            const data = await TripInformationService.getTripInformationReport(criteria);
            if (data?.totalRowCount > 0) {
                setFormData(prev => ({
                    ...prev,
                    selectedInstitution: institution,
                    reportData: data.reportData,
                    totalRecords: data.totalRowCount
                }));
            }
        } catch {
            setFormValidationMessage(reportMessage.CATCH_ERROR_MSG);
        } finally {
            setIsDataLoading(false);
        }
    };


    //const handleSearch = async (e) => {
    //    setShowError(false);
    //    setFormValidationMessage("");     
       
    //        setIsDataLoading(true);

    //        let searchCriteria = {
    //            PageSize: recordsPerPageRef.current,
    //            SkipRecords: ((formData.currentPage - 1) * recordsPerPageRef.current),
    //            SortName: formData.sortColumnName === "" ? "PrisonAssigned" : formData.sortColumnName,
    //            SortDirection: formData.sortColumnDirection === "" ? "ASC" : formData.sortColumnDirection,
    //            Institution: formData.selectedInstitution
    //        };

    //        let data = null;

    //        try {
    //            data = await TripInformationService.getTripInformationReport(searchCriteria);
    //            if (data.records.lemgth === 0) {
    //                setIsDataLoading(false);
    //                return;
    //            }
                
    //        }
    //        catch (ex) {

    //            setFormValidationMessage(reportMessage.CATCH_ERROR_MSG);
    //            setIsLoading(false);
    //        }

    //        if (data?.totalRowCount > 0) {
    //            setFormData(prevData => ({
    //                ...prevData,
    //                reportData: data.reportData,
    //                totalRecords: data.totalRowCount,
    //                sortColumnDirection: (formData.sortColumnDirection === "" ? "ASC" : formData.sortColumnDirection),
    //                sortColumnName: (formData.sortColumnName === "" ? "PrisonAssigned" : formData.sortColumnName)
    //            }));
    //        }
    //        else {
    //              //-- else condition
    //        };
            

    //        setIsDataLoading(false);
        
    //}

    // Reset Button

    const handleResetPage = async (e) => {
        setShowError(false);
        setFormValidationMessage("");

            setIsDataLoading(true);

            let searchCriteria = {
                PageSize: recordsPerPageRef.current,
                SkipRecords: ((formData.currentPage - 1) * recordsPerPageRef.current),
                SortName:  "PrisonAssigned",
                SortDirection: "ASC",
                Institution: ""
            };

            let data = null;

            try {
                data = await TripInformationService.getTripInformationReport(searchCriteria);
            }
            catch (ex) {
                setFormValidationMessage(reportMessage.CATCH_ERROR_MSG);
                setIsLoading(false);
            }

            if (data?.totalRowCount > 0) {
                setFormData(prevData => ({
                    ...prevData,
                    selectedInstitution: '',
                    reportData: data.reportData,
                    totalRecords: data.totalRowCount,
                    sortColumnDirection: "ASC",
                    sortColumnName: "PrisonAssigned"
                }));

                
            }
            else {
                
            };


            setIsDataLoading(false);
        
    }


    //functions
    const resetGrid = () => {

        setFormData((prevState) => ({
            ...prevState,
            selectedInstitution: '',
            totalRecords: 0,
            currentPage: 1,
            sortColumnName: 'PrisonAssigned',
            sortColumnDirection: 'ASC',
            reportData: [],
            isLoading: false
        }));

        setShowError(false);
        setFormValidationMessage("");
    };

    //export to Excel
    const handleExportToExcel = async (e) => {
        
        if (formData.selectedInstitution === "0") {
            setShowError(true);
            setFormValidationMessage(reportMessage.SELECT_INSTITUTION_MSG);
        }
        else {
            setIsDataLoading(true);

            let searchCriteria = {
                PageSize: 10000,
                SkipRecords: 0,
                SortName: 'PrisonAssigned',
                SortDirection: 'ASC',
                Institution: formData.selectedInstitution
            };

            let exportData = null;
            
            try {
                exportData = await TripInformationService.getTripInformationReport(searchCriteria);

                if (!exportData || !Array.isArray(exportData.reportData)) {
                    setShowError(true);
                    setFormValidationMessage(reportMessage.EXCEL_NO_RECORDS_MSG);
                    return;
                }

                if (exportData) {
                var cvsContent = "data:text/csv;charset=UTF-8,Search for Route (Prison),";
                cvsContent = cvsContent + (formData.selectedInstitution === "" ? "ALL INSTITUTIONS" : formData.selectedInstitution) + "\n" + 'PrisonAssigned,Route Type,Trip Name,Route,Return Trip,Vehicle Plate Number,Out Of Service, Capacity\n';

                exportData.reportData.map((tripViewList) => {

                    let outOfService = tripViewList.outOfService === '0' ? '' : 'Out Of Service';

                    cvsContent = cvsContent + tripViewList.prisonAssigned + ",\"" + tripViewList.routeType + "\"," + tripViewList.tripName + "," + tripViewList.route + "," + tripViewList.returnTrip + ","
                        + tripViewList.licensePlate + ",\"" + outOfService + "\"," + tripViewList.capacity + "\n"

                })

                var currentDate = new Date();
                var fileName = "TripViewReport_" + currentDate.toLocaleDateString() + ".csv";
                CommonService.downloadCSV(cvsContent, fileName);
            }
            else {
                setShowError(true);
                setFormValidationMessage(reportMessage.EXCEL_NO_RECORDS_MSG);
            }
        }
         catch (error) {
            setShowError(true);
            setFormValidationMessage(reportMessage.CATCH_ERROR_MSG);
            //comsole.log(ex);
        }
        finally {
            setIsDataLoading(false);
        }
            
      }
    };


    //Add New
    const handleAddNew = async (event) => {
        navigate("/TripInformation");
    };

    //Grid events
    const handleGridPageChange  = async (sortName, sortDirection, pageNumber) => {

        let searchCriteria = {
            PageSize: recordsPerPageRef.current,
            SkipRecords: ((pageNumber - 1) * recordsPerPageRef.current),
            SortName: sortName,
            SortDirection: sortDirection,
            Institution: formData.selectedInstitution
        };

        let data = null;

        try {

            data = await TripInformationService.getTripInformationReport(searchCriteria);
        }
        catch (ex) {
            comsole.log(ex);
        }

        if (data) {
            setFormData((prevData) => ({
                ...prevData,
                reportData: data.reportData,
                totalRecords: data.totalRowCount,
                currentPage: pageNumber
            }));
        };
    };

    

    const handleGridHeaderClick = async (event) => {
        var sortName = event.target.getAttribute("column");
        var sortDirection = formData.sortColumnDirection === 'ASC' ? 'DESC' : 'ASC';
        
        if (formData.totalRecords > 0) {
            let searchCriteria = {
                PageSize: recordsPerPageRef.current,
                SkipRecords: ((formData.currentPage - 1) * recordsPerPageRef.current),
                SortName: sortName,
                SortDirection: sortDirection,
                Institution: formData.selectedInstitution
            };

            let data = null;

            try {
                data = await TripInformationService.getTripInformationReport(searchCriteria);
            }
            catch (ex) {
                concole.log(ex);
            }

            if (data) {
                setFormData((prevData) => ({
                    ...prevData,
                    reportData: data.reportData,
                    totalRecords: data.totalRowCount,
                    sortColumnDirection: sortDirection,
                    sortColumnName: sortName
                }));
            };
        };
    };

    const handleGridRowClick = async (event) => {
        const element = event.target;
        const rowElement = element.closest("tr");
        
        const tripId = rowElement.getAttribute("trip-id");
        const tripInst = rowElement.getAttribute("trip-inst");
              

        if (userInstitutions.current.length == 0 || userInstitutions.current.includes(tripInst)) {
            //Pass the record ID 
            navigate("/TripInformation/" + tripId);
        }
        else {
        
            setModalState({
                ...modalState,
                showModal: true,
                showCloseButton: false,
                title: reportMessage.INSTITUTION_PERMISSION_TITLE,
                infoMessage: reportMessage.INSTITUTION_PERMISSION_MSG,
                closetoggleModal: async () => {
                    await setModalState({
                        ...modalState,
                        showModal: false,
                    });
                }
            });
        }
    };
    return (
        <>
            {/* Conditionally render the LoadingComponent */}
            {isLoading && <LoadingComponent />}
            <DialogModal ModalConfig={modalState} />
            {showError && (
                <div className="row m-1 mb-3  alert alert-danger" role="alert">{formValidationMessage}</div>
            )}
            <Card border="primary" key="1">
                <Card.Header className="card-header odrc-header-row" style={{ textAlign: 'right' }}>
                    <strong>Count: {formData.totalRecords}</strong>
                </Card.Header>
                <Card.Body className="card-body">
                    <div className="form-group row mb-2">
                        <b>Note:</b>This screen is the HUB Route Information Screen, the information on this screen is entered in TRIPI and is a roster of the HUB bus route.
                    </div>
                    <br />
                    <div className="form-group row mb-2">
                        <label className="control-label col-md-4 col-sm-4 odrc-main-label-text" htmlFor="Search for Route">Search for Route (Prison)</label>
                        <div className="col-md-4">
                            {/*<select value={formData.selectedInstitution} className="form-control" name="InstitutionList" onChange={(e) => handleInstitutionChange(e)}>*/}
                            {/*    */}{/*<option value="">Select Institution</option>*/}
                            {/*    <option value="">ALL INSTITUTIONS</option>*/}
                            {/*    {*/}
                            {/*        formData && formData.institutionList?.map((institution) => (*/}
                            {/*            <option key={institution.instCode} id={institution.institutionId} value={institution.instCode}> {institution.institutionName} </option>*/}
                            {/*        ))*/}
                            {/*    }*/}
                            {/*</select>*/}
                            <select
                                value={formData.selectedInstitution}
                                className="form-control"
                                name="InstitutionList"
                                onChange={async e => {
                                    const value = e.target.value;
                                    clearGrid(value);

                                    if (value === '') {
                                        // “ALL INSTITUTIONS” picked
                                        await handleResetPage();
                                    } else {
                                        // a real institution code picked
                                        await handleSearch(null, value);
                                    }
                                }}
                            >
                                <option value="">ALL INSTITUTIONS</option>
                                {formData.institutionList.map(inst => (
                                    <option
                                        key={inst.instCode}
                                        value={inst.instCode}
                                    >
                                        {inst.institutionName}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>
                    <br />
                    <div className="form-group row mb-2 mt-2">
                        <div className="col-md-4 col-sm-4 col-xs-4"></div>
                        <div className="col-md-2 col-sm-2 col-xs-2 center align-content-center" style={{ textAlign: "left" }}>
                            
                            {/*<input type="button" name="btnSearch" value="Search" className="btn btn-primary" style={{ display: "none" }} onClick={async (e) => await handleSearch(e)} />*/}
                            {/*<input type="button" name="btnReset" value="Reset" className="btn btn-secondary" ref={resetBtnRef} style={{ display: "none" }} onClick={async (e) => await handleResetPage(e)} />*/}
                        </div>
                      
                        <div className="col-md-6 col-sm-6 col-xs-6 center align-content-center" style={{ textAlign: "right" }}>
                            <input type="button" name="btnAddNew" value="Add New Trip" className="btn btn-success" onClick={(e) => handleAddNew(e)} />

                            <input type="button" name="btnExcelReport" value="Export" className="btn btn-danger" onClick={(e) => handleExportToExcel(e)} />
                        </div>
                    </div>
                    <div style={{ display: 'flex', justifycontent: 'flex-start' }}>
                        {
                            formData.totalRecords > recordsPerPageRef.current && (
                                <GridPagination
                                    totalRecords={formData.totalRecords}
                                    currentPage={formData.currentPage}
                                    onPageChange={handleGridPageChange}
                                    recordsPerPage={recordsPerPageRef.current}
                                    sortDirection={formData.sortColumnDirection}
                                    sortColumnName={formData.sortColumnName}
                                />
                            )
                        }
                    </div>
                    
                    <br />
                    <div className="row">
                        <div className="col-md-12">
                            <table className="table table-hover table-bordered">
                                <thead>
                                    <tr className="odrc-table-header-row">
                                        <th column="PrisonAssigned" className="sort" onClick={handleGridHeaderClick}>Prison Assigned
                                            <span className="sort-column-order">
                                                {
                                                    formData.sortColumnName === "PrisonAssigned" && (formData.sortColumnDirection === "ASC" ?
                                                        <i className="bi bi-sort-down"></i> : <i className="bi bi-sort-up"></i>)
                                                }
                                            </span>
                                        </th>
                                        <th column="RouteType" className="sort" onClick={handleGridHeaderClick}>Route Type
                                            <span className="sort-column-order">
                                                {
                                                    formData.sortColumnName === "RouteType" && (formData.sortColumnDirection === "ASC" ?
                                                        <i className="bi bi-sort-down"></i> : <i className="bi bi-sort-up"></i>)
                                                }
                                            </span>
                                        </th>
                                        <th column="TripName" className="sort" onClick={handleGridHeaderClick}>Trip Name
                                            <span className="sort-column-order">
                                                {
                                                    formData.sortColumnName === "TripName" && (formData.sortColumnDirection === "ASC" ?
                                                        <i className="bi bi-sort-down"></i> : <i className="bi bi-sort-up"></i>)
                                                }
                                            </span>
                                        </th>
                                        <th column="Route" className="sort" onClick={handleGridHeaderClick}>Route
                                            <span className="sort-column-order">
                                                {
                                                    formData.sortColumnName === "Route" && (formData.sortColumnDirection === "ASC" ?
                                                        <i className="bi bi-sort-down"></i> : <i className="bi bi-sort-up"></i>)
                                                }
                                            </span>
                                        </th>
                                        <th>Return trip</th>
                                        <th>Vehicle Plate Number</th>
                                        <th>Out Of Service</th>
                                        <th>Capacity</th>
                                        
                                        <th>Prison Transportation Directory</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {
                                        isDataLoading ? (<tr>
                                            <td colSpan="8" className="odrc-main-label-text" style={{ textAlign: "center" }}><LoadingComponent /></td></tr>) : (formData.reportData.length > 0 ? (formData.reportData.map((tripViewList, index) => (
                                                <tr key={index} trip-id={tripViewList.id} trip-inst={tripViewList.prisonAssigned} onClick={handleGridRowClick}>
                                                    <td style={{ textAlign: "center" }}>{tripViewList.prisonAssigned}</td>
                                                    <td>{tripViewList.routeType}</td>
                                                    <td style={{ textAlign: "center" }}>{tripViewList.tripName}</td>
                                                    <td style={{ textAlign: "center" }}>{tripViewList.route}</td>
                                                    <td style={{ textAlign: "center" }}>{tripViewList.returnTrip}</td>
                                                    <td style={{ textAlign: "center" }}>{tripViewList.licensePlate}</td>
                                                    <td style={{ textAlign: "center" }}>{tripViewList.outOfService === '1' ?
                                                        <span style={{ color: 'red', fontWeight: 'bold' }}> Out Of Service</span> : <span></span>}
                                                    </td>
                                                    <td style={{ textAlign: "center" }}>{tripViewList.capacity}</td>
                                                    <td style={{ textAlign: "center" }}>
                                                    
                                                        <Link to={`/StaffContactReportPrint/${(tripViewList.prisonAssigned == "" ? "ALL"
                                                            : tripViewList.prisonAssigned)}/${formData.currentPage}/${formData.sortColumnDirection}/${formData.sortColumnName}`}
                                                            target="_blank" rel="noopener noreferrer" type="button" className="btn btn-info" onClick={(e) => { e.stopPropagation(); }} >Prison Directory</Link>
                                                    </td>
                                                </tr>

                                            ))) : (
                                                <tr>
                                                    <td colSpan="8" className="odrc-main-label-text" style={{ textAlign: "center" }}> No Records Found
                                                    </td>
                                                </tr>
                                            ))
                                    }
                                </tbody>
                            </table>
                            <div style={{display:'flex', justifycontent: 'flex-start' }} >
                                {
                                    formData.totalRecords > recordsPerPageRef.current && (
                                        <GridPagination
                                            totalRecords={formData.totalRecords}
                                            currentPage={formData.currentPage}
                                            onPageChange={handleGridPageChange}
                                            recordsPerPage={recordsPerPageRef.current}
                                            sortDirection={formData.sortColumnDirection}
                                            sortColumnName={formData.sortColumnName}
                                        />
                                    )
                                }
                            </div>
                        </div>
                    </div>
                </Card.Body>
            </Card>
        </>
    );
}

export default TripList;
